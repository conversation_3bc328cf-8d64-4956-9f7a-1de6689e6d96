# H20芯片被约谈背后：国产算力卡的历史性机会来了

今天一个重磅消息刷屏了，国家网信办约谈英伟达，要求就H20芯片的安全漏洞问题进行说明。简单说就是这芯片有远程监控功能，就像你的手机被人装了监控软件一样，你的所有操作都能被远程看到，甚至还能被远程关闭。

我今晚专门去蹲了一些一手分析资料，得到一些新的看法。首先是H20的订单情况，第一批H20订单上报总量不到40万片，字节跳动不到10万片，阿里大概13-14万片，腾讯7-8万片，百度也有5万片左右。但从上周开始（别问，懂得dd），中小企业开始陆续撤回需求，连大厂也暂停了采购。

## 国产替代的历史性转折点

我仔细琢磨了，这次事件的意义可能被很多人低估了。以前企业选择国产芯片，更多是被迫的，因为买不到更好的。但现在不一样了，是主动选择，因为安全考虑。

从技术角度看，华为昇腾910C的综合性能已经达到H100的60%左右，而H20的AI算力只有H100的不到15%。换句话说，昇腾910C的性能其实是超过H20的。更关键的是，昇腾910B的部分性能甚至已经不输H20了。

根据我查到的消息来看，今年字节有10万卡的910C订单，阿里大概有7-8万卡的920C订单。这些订单不是概念，是真金白银的需求。百度等公司也有1-2万卡的910C小订单。

这就像以前大家都去肯德基吃汉堡，现在发现肯德基的汉堡可能被人动了手脚，而隔壁的中式汉堡店味道也不错，价格还便宜，你说大家会怎么选？

## 技术差距正在快速缩小

很多人担心国产芯片技术不行，这个担心以前确实存在，但现在情况在快速改变。

华为昇腾系列的技术路线很清晰，910B是主力产品，910C正在加速生产，920系列也在4月份发布了。从性能上看，910C在推理性能上已经能达到H100的60%，这个水平用来做大模型训练和推理是完全够用的。

更重要的是生态建设。华为在AI芯片生态上投入巨大，从底层的昇腾CANN计算架构，到上层的MindSpore框架，再到各种开发工具，基本形成了完整的生态链。字节、阿里这些大厂之所以敢下大订单，就是因为生态已经相对成熟了。

还有一个很多人忽视的点，就是供应链的稳定性。H20虽然解禁了，但供应链还是掌握在别人手里，说断就断。而国产芯片虽然性能可能还有差距，但供应链是可控的，这对企业的长期规划非常重要。

综上所述，我觉得这次事件更可能是国产算力芯片商业化的一个重要转折点。

首先是华为产业链。虽然华为不是上市公司，但华为的供应商会直接受益。**比如海光信息**，它的DCU芯片在AI推理方面有不错的表现，而且已经有实际的商业化应用。**寒武纪**的思元系列芯片在端侧AI方面也有优势。

其次是存储和配套产业链。AI芯片需要大量的高速存储，**兆易创新、北京君正这些存储芯片公司会间接受益**。还有像景嘉微这样的GPU公司，虽然主要做图形处理，但在通用计算方面也有布局。

第三是软件生态公司。AI芯片不是孤立存在的，需要完整的软件栈支持。**中科曙光、浪潮信息这些做服务器和系统集成的公司**，在国产AI芯片生态建设中扮演重要角色。

当然，我也要提醒大家，这个板块的投资风险不小。技术迭代很快，今天的领先者明天可能就被超越了。而且商业化进程存在不确定性，订单能不能持续放量还需要观察。

## 大熊碎碎念

从这次H20事件可以看出，科技自主可控不是口号，而是现实需要。当你的核心技术被人掌控，还被装了后门的时候，再便宜也不敢用。

国产替代这个主题我们讲了很多年，但真正的机会可能就在现在。不是因为政策推动，而是因为市场需求的真实转变。企业开始主动选择国产方案，这个意义是不一样的。

投资上，我建议关注那些有真实技术积累、有实际订单验证的公司。不要追概念，要看实际的商业化进展。

这个赛道的投资周期可能比较长，需要耐心。但如果国产替代真的起来了，那收益也是相当可观的。毕竟，这可是一个万亿级别的市场。
